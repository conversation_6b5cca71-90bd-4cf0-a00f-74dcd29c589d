<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <!--resource="db.properties"-->
    <properties >
        <!-- 启用默认占位符 -->
        <property name="org.apache.ibatis.parsing.PropertyParser.enable-default-value" value="true"/>
    </properties>
    <typeAliases>
        <!--<typeAlias type="com.xjy.test.mybatis.pojo.TempCommand" alias="TempCommand"/>-->
        <typeAlias type="com.xjy.pojo.DeviceTmp" alias="DeviceTmp"/>
        <typeAlias type="com.xjy.pojo.DBCommand" alias="DBCommand"/>
        <typeAlias type="com.xjy.pojo.DBCollector" alias="DBCollector"/>
        <typeAlias type="com.xjy.pojo.DBMeter" alias="DBMeter"/>
        <typeAlias type="com.xjy.pojo.Scheme" alias="Scheme"/>
        <typeAlias type="com.xjy.entity.TAmmeter" alias="TAmmeter"/>
        <typeAlias type="com.xjy.entity.TAmmeterHistorian" alias="TAmmeterHistorian"/>
    </typeAliases>
    <environments default="development">
        <environment id="development">
            <transactionManager type="JDBC"/>
<!--            <dataSource type="POOLED">-->
<!--                <property name="driver" value="${driver:com.microsoft.sqlserver.jdbc.SQLServerDriver}"/>-->
<!--                <property name="url" value="jdbc:sqlserver://${dbServer:127.0.0.1}:3306;DatabaseName=${database:QBTT}"/>-->
<!--                <property name="username" value="${dbId:sa}"/>-->
<!--                <property name="password" value="${dbPwd:kilotone@2012}"/>-->
<!--            </dataSource>-->
            <dataSource type="POOLED">
                <property name="driver" value="com.mysql.cj.jdbc.Driver"/>
                <property name="url" value="*********************************************************************************************************************"/>
                <property name="username" value="root"/>
                <property name="password" value="myc714AL"/>
            </dataSource>
        </environment>
    </environments>
    <mappers>
        <!--<mapper resource="com/xjy/test/mybatis/mappers/TempCommandMapper.xml"/>-->
        <mapper resource="mappers/CommandMapper.xml"/>
        <mapper resource="mappers/CenterMapper.xml"/>
        <mapper resource="mappers/DeviceTmpMapper.xml"/>
        <mapper resource="mappers/TAmmeterMapper.xml"/>
        <mapper resource="mappers/TAmmeterHistorianMapper.xml"/>
    </mappers>
</configuration>
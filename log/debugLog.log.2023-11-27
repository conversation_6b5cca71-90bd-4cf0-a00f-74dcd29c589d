DEBUG 2023-11-27 16:32:00,348: io.netty.util.internal.logging.InternalLoggerFactory 
 Using Log4J as the default logging framework 
DEBUG 2023-11-27 16:32:00,398: io.netty.channel.MultithreadEventLoopGroup 
 -Dio.netty.eventLoopThreads: 8 
DEBUG 2023-11-27 16:32:00,501: io.netty.util.internal.PlatformDependent0 
 java.nio.Buffer.address: available 
DEBUG 2023-11-27 16:32:00,501: io.netty.util.internal.PlatformDependent0 
 sun.misc.Unsafe.theUnsafe: available 
DEBUG 2023-11-27 16:32:00,502: io.netty.util.internal.PlatformDependent0 
 sun.misc.Unsafe.copyMemory: available 
DEBUG 2023-11-27 16:32:00,502: io.netty.util.internal.PlatformDependent0 
 java.nio.Bits.unaligned: true 
DEBUG 2023-11-27 16:32:00,503: io.netty.util.internal.PlatformDependent 
 Platform: Windows 
DEBUG 2023-11-27 16:32:00,503: io.netty.util.internal.PlatformDependent 
 Java version: 8 
DEBUG 2023-11-27 16:32:00,503: io.netty.util.internal.PlatformDependent 
 -Dio.netty.noUnsafe: false 
DEBUG 2023-11-27 16:32:00,503: io.netty.util.internal.PlatformDependent 
 sun.misc.Unsafe: available 
DEBUG 2023-11-27 16:32:00,504: io.netty.util.internal.PlatformDependent 
 -Dio.netty.noJavassist: false 
DEBUG 2023-11-27 16:32:00,505: io.netty.util.internal.PlatformDependent 
 Javassist: unavailable 
DEBUG 2023-11-27 16:32:00,505: io.netty.util.internal.PlatformDependent 
 You don't have Javassist in your class path or you don't have enough permission to load dynamically generated classes.  Please check the configuration for better performance. 
DEBUG 2023-11-27 16:32:00,506: io.netty.util.internal.PlatformDependent 
 -Dio.netty.tmpdir: C:\Users\<USER>\AppData\Local\Temp (java.io.tmpdir) 
DEBUG 2023-11-27 16:32:00,506: io.netty.util.internal.PlatformDependent 
 -Dio.netty.bitMode: 64 (sun.arch.data.model) 
DEBUG 2023-11-27 16:32:00,506: io.netty.util.internal.PlatformDependent 
 -Dio.netty.noPreferDirect: false 
DEBUG 2023-11-27 16:32:00,507: io.netty.channel.nio.NioEventLoop 
 -Dio.netty.noKeySetOptimization: false 
DEBUG 2023-11-27 16:32:00,507: io.netty.channel.nio.NioEventLoop 
 -Dio.netty.selectorAutoRebuildThreshold: 512 
DEBUG 2023-11-27 16:32:01,336: io.netty.util.ResourceLeakDetector 
 -Dio.netty.leakDetectionLevel: simple 
DEBUG 2023-11-27 16:32:01,349: io.netty.channel.DefaultChannelId 
 -Dio.netty.processId: 11928 (auto-detected) 
DEBUG 2023-11-27 16:32:02,071: io.netty.channel.DefaultChannelId 
 -Dio.netty.machineId: c8:3d:d4:ff:fe:69:d6:a5 (auto-detected) 
DEBUG 2023-11-27 16:32:02,076: io.netty.util.internal.ThreadLocalRandom 
 -Dio.netty.initialSeedUniquifier: 0x844e643e956f2e5c (took 50 ms) 
DEBUG 2023-11-27 16:32:02,203: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.numHeapArenas: 4 
DEBUG 2023-11-27 16:32:02,203: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.numDirectArenas: 4 
DEBUG 2023-11-27 16:32:02,203: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.pageSize: 8192 
DEBUG 2023-11-27 16:32:02,203: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.maxOrder: 11 
DEBUG 2023-11-27 16:32:02,203: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.chunkSize: 16777216 
DEBUG 2023-11-27 16:32:02,203: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.tinyCacheSize: 512 
DEBUG 2023-11-27 16:32:02,203: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.smallCacheSize: 256 
DEBUG 2023-11-27 16:32:02,203: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.normalCacheSize: 64 
DEBUG 2023-11-27 16:32:02,203: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.maxCachedBufferCapacity: 32768 
DEBUG 2023-11-27 16:32:02,203: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.cacheTrimInterval: 8192 
DEBUG 2023-11-27 16:32:02,221: io.netty.buffer.ByteBufUtil 
 -Dio.netty.allocator.type: pooled 
DEBUG 2023-11-27 16:32:02,221: io.netty.buffer.ByteBufUtil 
 -Dio.netty.threadLocalDirectBufferSize: 65536 
DEBUG 2023-11-27 16:32:04,627: io.netty.util.NetUtil 
 Loopback interface: lo (Software Loopback Interface 1, 127.0.0.1) 
DEBUG 2023-11-27 16:32:04,627: io.netty.util.NetUtil 
 \proc\sys\net\core\somaxconn: 200 (non-existent) 

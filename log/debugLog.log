DEBUG 2025-06-22 14:48:57,977: io.netty.util.internal.logging.InternalLoggerFactory 
 Using Log4J as the default logging framework 
DEBUG 2025-06-22 14:48:57,984: io.netty.channel.MultithreadEventLoopGroup 
 -Dio.netty.eventLoopThreads: 44 
DEBUG 2025-06-22 14:48:58,017: io.netty.util.internal.PlatformDependent0 
 java.nio.Buffer.address: available 
DEBUG 2025-06-22 14:48:58,017: io.netty.util.internal.PlatformDependent0 
 sun.misc.Unsafe.theUnsafe: available 
DEBUG 2025-06-22 14:48:58,018: io.netty.util.internal.PlatformDependent0 
 sun.misc.Unsafe.copyMemory: available 
DEBUG 2025-06-22 14:48:58,018: io.netty.util.internal.PlatformDependent0 
 java.nio.Bits.unaligned: true 
DEBUG 2025-06-22 14:48:58,018: io.netty.util.internal.PlatformDependent 
 Platform: Windows 
DEBUG 2025-06-22 14:48:58,018: io.netty.util.internal.PlatformDependent 
 Java version: 8 
DEBUG 2025-06-22 14:48:58,018: io.netty.util.internal.PlatformDependent 
 -Dio.netty.noUnsafe: false 
DEBUG 2025-06-22 14:48:58,018: io.netty.util.internal.PlatformDependent 
 sun.misc.Unsafe: available 
DEBUG 2025-06-22 14:48:58,018: io.netty.util.internal.PlatformDependent 
 -Dio.netty.noJavassist: false 
DEBUG 2025-06-22 14:48:58,020: io.netty.util.internal.PlatformDependent 
 Javassist: unavailable 
DEBUG 2025-06-22 14:48:58,020: io.netty.util.internal.PlatformDependent 
 You don't have Javassist in your class path or you don't have enough permission to load dynamically generated classes.  Please check the configuration for better performance. 
DEBUG 2025-06-22 14:48:58,020: io.netty.util.internal.PlatformDependent 
 -Dio.netty.tmpdir: C:\Users\<USER>\AppData\Local\Temp (java.io.tmpdir) 
DEBUG 2025-06-22 14:48:58,020: io.netty.util.internal.PlatformDependent 
 -Dio.netty.bitMode: 64 (sun.arch.data.model) 
DEBUG 2025-06-22 14:48:58,021: io.netty.util.internal.PlatformDependent 
 -Dio.netty.noPreferDirect: false 
DEBUG 2025-06-22 14:48:58,021: io.netty.channel.nio.NioEventLoop 
 -Dio.netty.noKeySetOptimization: false 
DEBUG 2025-06-22 14:48:58,021: io.netty.channel.nio.NioEventLoop 
 -Dio.netty.selectorAutoRebuildThreshold: 512 
DEBUG 2025-06-22 14:48:58,112: io.netty.util.ResourceLeakDetector 
 -Dio.netty.leakDetectionLevel: simple 
DEBUG 2025-06-22 14:48:58,127: io.netty.channel.DefaultChannelId 
 -Dio.netty.processId: 4688 (auto-detected) 
DEBUG 2025-06-22 14:48:58,414: io.netty.channel.DefaultChannelId 
 -Dio.netty.machineId: 6c:2f:80:ff:fe:5d:f5:1b (auto-detected) 
DEBUG 2025-06-22 14:48:58,415: io.netty.util.internal.ThreadLocalRandom 
 -Dio.netty.initialSeedUniquifier: 0x84be57c3d5e232ff (took 1 ms) 
DEBUG 2025-06-22 14:48:58,430: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.numHeapArenas: 22 
DEBUG 2025-06-22 14:48:58,430: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.numDirectArenas: 22 
DEBUG 2025-06-22 14:48:58,430: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.pageSize: 8192 
DEBUG 2025-06-22 14:48:58,430: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.maxOrder: 11 
DEBUG 2025-06-22 14:48:58,430: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.chunkSize: 16777216 
DEBUG 2025-06-22 14:48:58,430: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.tinyCacheSize: 512 
DEBUG 2025-06-22 14:48:58,430: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.smallCacheSize: 256 
DEBUG 2025-06-22 14:48:58,430: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.normalCacheSize: 64 
DEBUG 2025-06-22 14:48:58,430: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.maxCachedBufferCapacity: 32768 
DEBUG 2025-06-22 14:48:58,430: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.cacheTrimInterval: 8192 
DEBUG 2025-06-22 14:48:58,433: io.netty.buffer.ByteBufUtil 
 -Dio.netty.allocator.type: pooled 
DEBUG 2025-06-22 14:48:58,434: io.netty.buffer.ByteBufUtil 
 -Dio.netty.threadLocalDirectBufferSize: 65536 
DEBUG 2025-06-22 14:48:58,695: io.netty.util.NetUtil 
 Loopback interface: lo (Software Loopback Interface 1, 127.0.0.1) 
DEBUG 2025-06-22 14:48:58,696: io.netty.util.NetUtil 
 \proc\sys\net\core\somaxconn: 200 (non-existent) 
DEBUG 2025-06-22 15:08:07,367: io.netty.util.internal.logging.InternalLoggerFactory 
 Using Log4J as the default logging framework 
DEBUG 2025-06-22 15:08:07,371: io.netty.channel.MultithreadEventLoopGroup 
 -Dio.netty.eventLoopThreads: 44 
DEBUG 2025-06-22 15:08:07,397: io.netty.util.internal.PlatformDependent0 
 java.nio.Buffer.address: available 
DEBUG 2025-06-22 15:08:07,397: io.netty.util.internal.PlatformDependent0 
 sun.misc.Unsafe.theUnsafe: available 
DEBUG 2025-06-22 15:08:07,397: io.netty.util.internal.PlatformDependent0 
 sun.misc.Unsafe.copyMemory: available 
DEBUG 2025-06-22 15:08:07,398: io.netty.util.internal.PlatformDependent0 
 java.nio.Bits.unaligned: true 
DEBUG 2025-06-22 15:08:07,398: io.netty.util.internal.PlatformDependent 
 Platform: Windows 
DEBUG 2025-06-22 15:08:07,398: io.netty.util.internal.PlatformDependent 
 Java version: 8 
DEBUG 2025-06-22 15:08:07,398: io.netty.util.internal.PlatformDependent 
 -Dio.netty.noUnsafe: false 
DEBUG 2025-06-22 15:08:07,398: io.netty.util.internal.PlatformDependent 
 sun.misc.Unsafe: available 
DEBUG 2025-06-22 15:08:07,398: io.netty.util.internal.PlatformDependent 
 -Dio.netty.noJavassist: false 
DEBUG 2025-06-22 15:08:07,398: io.netty.util.internal.PlatformDependent 
 Javassist: unavailable 
DEBUG 2025-06-22 15:08:07,399: io.netty.util.internal.PlatformDependent 
 You don't have Javassist in your class path or you don't have enough permission to load dynamically generated classes.  Please check the configuration for better performance. 
DEBUG 2025-06-22 15:08:07,399: io.netty.util.internal.PlatformDependent 
 -Dio.netty.tmpdir: C:\Users\<USER>\AppData\Local\Temp (java.io.tmpdir) 
DEBUG 2025-06-22 15:08:07,399: io.netty.util.internal.PlatformDependent 
 -Dio.netty.bitMode: 64 (sun.arch.data.model) 
DEBUG 2025-06-22 15:08:07,399: io.netty.util.internal.PlatformDependent 
 -Dio.netty.noPreferDirect: false 
DEBUG 2025-06-22 15:08:07,400: io.netty.channel.nio.NioEventLoop 
 -Dio.netty.noKeySetOptimization: false 
DEBUG 2025-06-22 15:08:07,400: io.netty.channel.nio.NioEventLoop 
 -Dio.netty.selectorAutoRebuildThreshold: 512 
DEBUG 2025-06-22 15:08:07,508: io.netty.util.ResourceLeakDetector 
 -Dio.netty.leakDetectionLevel: simple 
DEBUG 2025-06-22 15:08:07,523: io.netty.channel.DefaultChannelId 
 -Dio.netty.processId: 17296 (auto-detected) 
DEBUG 2025-06-22 15:08:07,874: io.netty.channel.DefaultChannelId 
 -Dio.netty.machineId: 6c:2f:80:ff:fe:5d:f5:1b (auto-detected) 
DEBUG 2025-06-22 15:08:07,876: io.netty.util.internal.ThreadLocalRandom 
 -Dio.netty.initialSeedUniquifier: 0x174f982b96e49c1c (took 1 ms) 
DEBUG 2025-06-22 15:08:07,890: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.numHeapArenas: 22 
DEBUG 2025-06-22 15:08:07,890: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.numDirectArenas: 22 
DEBUG 2025-06-22 15:08:07,890: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.pageSize: 8192 
DEBUG 2025-06-22 15:08:07,890: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.maxOrder: 11 
DEBUG 2025-06-22 15:08:07,890: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.chunkSize: 16777216 
DEBUG 2025-06-22 15:08:07,890: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.tinyCacheSize: 512 
DEBUG 2025-06-22 15:08:07,890: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.smallCacheSize: 256 
DEBUG 2025-06-22 15:08:07,890: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.normalCacheSize: 64 
DEBUG 2025-06-22 15:08:07,890: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.maxCachedBufferCapacity: 32768 
DEBUG 2025-06-22 15:08:07,890: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.cacheTrimInterval: 8192 
DEBUG 2025-06-22 15:08:07,894: io.netty.buffer.ByteBufUtil 
 -Dio.netty.allocator.type: pooled 
DEBUG 2025-06-22 15:08:07,894: io.netty.buffer.ByteBufUtil 
 -Dio.netty.threadLocalDirectBufferSize: 65536 
DEBUG 2025-06-22 15:08:08,091: io.netty.util.NetUtil 
 Loopback interface: lo (Software Loopback Interface 1, 127.0.0.1) 
DEBUG 2025-06-22 15:08:08,092: io.netty.util.NetUtil 
 \proc\sys\net\core\somaxconn: 200 (non-existent) 
DEBUG 2025-06-22 15:09:13,557: io.netty.util.internal.logging.InternalLoggerFactory 
 Using Log4J as the default logging framework 
DEBUG 2025-06-22 15:09:13,562: io.netty.channel.MultithreadEventLoopGroup 
 -Dio.netty.eventLoopThreads: 44 
DEBUG 2025-06-22 15:09:13,584: io.netty.util.internal.PlatformDependent0 
 java.nio.Buffer.address: available 
DEBUG 2025-06-22 15:09:13,586: io.netty.util.internal.PlatformDependent0 
 sun.misc.Unsafe.theUnsafe: available 
DEBUG 2025-06-22 15:09:13,586: io.netty.util.internal.PlatformDependent0 
 sun.misc.Unsafe.copyMemory: available 
DEBUG 2025-06-22 15:09:13,586: io.netty.util.internal.PlatformDependent0 
 java.nio.Bits.unaligned: true 
DEBUG 2025-06-22 15:09:13,586: io.netty.util.internal.PlatformDependent 
 Platform: Windows 
DEBUG 2025-06-22 15:09:13,586: io.netty.util.internal.PlatformDependent 
 Java version: 8 
DEBUG 2025-06-22 15:09:13,586: io.netty.util.internal.PlatformDependent 
 -Dio.netty.noUnsafe: false 
DEBUG 2025-06-22 15:09:13,586: io.netty.util.internal.PlatformDependent 
 sun.misc.Unsafe: available 
DEBUG 2025-06-22 15:09:13,586: io.netty.util.internal.PlatformDependent 
 -Dio.netty.noJavassist: false 
DEBUG 2025-06-22 15:09:13,587: io.netty.util.internal.PlatformDependent 
 Javassist: unavailable 
DEBUG 2025-06-22 15:09:13,587: io.netty.util.internal.PlatformDependent 
 You don't have Javassist in your class path or you don't have enough permission to load dynamically generated classes.  Please check the configuration for better performance. 
DEBUG 2025-06-22 15:09:13,587: io.netty.util.internal.PlatformDependent 
 -Dio.netty.tmpdir: C:\Users\<USER>\AppData\Local\Temp (java.io.tmpdir) 
DEBUG 2025-06-22 15:09:13,587: io.netty.util.internal.PlatformDependent 
 -Dio.netty.bitMode: 64 (sun.arch.data.model) 
DEBUG 2025-06-22 15:09:13,587: io.netty.util.internal.PlatformDependent 
 -Dio.netty.noPreferDirect: false 
DEBUG 2025-06-22 15:09:13,587: io.netty.channel.nio.NioEventLoop 
 -Dio.netty.noKeySetOptimization: false 
DEBUG 2025-06-22 15:09:13,587: io.netty.channel.nio.NioEventLoop 
 -Dio.netty.selectorAutoRebuildThreshold: 512 
DEBUG 2025-06-22 15:09:13,689: io.netty.util.ResourceLeakDetector 
 -Dio.netty.leakDetectionLevel: simple 
DEBUG 2025-06-22 15:09:13,700: io.netty.channel.DefaultChannelId 
 -Dio.netty.processId: 25712 (auto-detected) 
DEBUG 2025-06-22 15:09:14,048: io.netty.channel.DefaultChannelId 
 -Dio.netty.machineId: 6c:2f:80:ff:fe:5d:f5:1b (auto-detected) 
DEBUG 2025-06-22 15:09:14,049: io.netty.util.internal.ThreadLocalRandom 
 -Dio.netty.initialSeedUniquifier: 0x6d0e14d629b86075 (took 1 ms) 
DEBUG 2025-06-22 15:09:14,063: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.numHeapArenas: 22 
DEBUG 2025-06-22 15:09:14,063: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.numDirectArenas: 22 
DEBUG 2025-06-22 15:09:14,063: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.pageSize: 8192 
DEBUG 2025-06-22 15:09:14,063: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.maxOrder: 11 
DEBUG 2025-06-22 15:09:14,063: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.chunkSize: 16777216 
DEBUG 2025-06-22 15:09:14,063: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.tinyCacheSize: 512 
DEBUG 2025-06-22 15:09:14,063: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.smallCacheSize: 256 
DEBUG 2025-06-22 15:09:14,063: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.normalCacheSize: 64 
DEBUG 2025-06-22 15:09:14,063: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.maxCachedBufferCapacity: 32768 
DEBUG 2025-06-22 15:09:14,063: io.netty.buffer.PooledByteBufAllocator 
 -Dio.netty.allocator.cacheTrimInterval: 8192 
DEBUG 2025-06-22 15:09:14,066: io.netty.buffer.ByteBufUtil 
 -Dio.netty.allocator.type: pooled 
DEBUG 2025-06-22 15:09:14,066: io.netty.buffer.ByteBufUtil 
 -Dio.netty.threadLocalDirectBufferSize: 65536 
DEBUG 2025-06-22 15:09:14,262: io.netty.util.NetUtil 
 Loopback interface: lo (Software Loopback Interface 1, 127.0.0.1) 
DEBUG 2025-06-22 15:09:14,262: io.netty.util.NetUtil 
 \proc\sys\net\core\somaxconn: 200 (non-existent) 

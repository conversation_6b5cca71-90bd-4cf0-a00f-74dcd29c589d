2025-06-22T14:34:16.928
�ޥ��ChannelHandlerContext(XtProtocolDecoder#0, [id: 0x567847e1, /192.168.1.200:4104 => /192.168.1.98:2090])

2025-06-22T14:34:19.859
�!60�-h800104066��o
XtMsgBody{C=c9, A1=8001, A2=[15, 226], A3=00, AFN=02, SEQ=70, Fn=00 00 01 00 
, data=}  centerAddr:800104066

2025-06-22T14:34:19.860
��8

2025-06-22T14:34:19.861
Could not initialize class com.xjy.test.mybatis.util.MyBatisUtil

2025-06-22T14:35:19.828
�ޥ��ChannelHandlerContext(XtProtocolDecoder#0, [id: 0x7bfe287d, /192.168.1.200:4106 => /192.168.1.98:2090])

2025-06-22T14:35:19.829
����-h800104066
�e�ctxChannelHandlerContext(XtMessageHandler#0, [id: 0x567847e1, /192.168.1.200:4104 :> /192.168.1.98:2090])  false
��ctx:ChannelHandlerContext(XtProtocolDecoder#0, [id: 0x7bfe287d, /192.168.1.200:4106 => /192.168.1.98:2090])    true

2025-06-22T14:35:22.888
��8

2025-06-22T14:35:22.889
Could not initialize class com.xjy.test.mybatis.util.MyBatisUtil

2025-06-22T14:36:22.882
�ޥ��ChannelHandlerContext(XtProtocolDecoder#0, [id: 0x9461720d, /192.168.1.200:4108 => /192.168.1.98:2090])

2025-06-22T14:36:22.883
����-h800104066
�e�ctxChannelHandlerContext(XtMessageHandler#0, [id: 0x7bfe287d, /192.168.1.200:4106 :> /192.168.1.98:2090])  false
��ctx:ChannelHandlerContext(XtProtocolDecoder#0, [id: 0x9461720d, /192.168.1.200:4108 => /192.168.1.98:2090])    true

2025-06-22T14:36:25.918
�-h800104066
ctxChannelHandlerContext(XtMessageHandler#0, [id: 0x9461720d, /192.168.1.200:4108 => /192.168.1.98:2090])  true

2025-06-22T14:36:25.919
����222
68 32 00 32 00 68 0b 01 ffffff80 ffffffe2 0f fffffff2 00 62 00 00 01 00 ffffffd2 16 

2025-06-22T14:36:26.987
60�˥�666
[104, 74, 0, 74, 0, 104, 201, 1, 128, 226, 15, 0, 2, 115, 0, 0, 4, 0, 38, 53, 20, 34, 6, 37, 112, 22]

2025-06-22T14:36:26.989
�-h800104066
ctxChannelHandlerContext(XtMessageHandler#0, [id: 0x9461720d, /192.168.1.200:4108 => /192.168.1.98:2090])  true

2025-06-22T14:36:26.989
����222
68 32 00 32 00 68 0b 01 ffffff80 ffffffe2 0f fffffff2 00 63 00 00 01 00 ffffffd3 16 

2025-06-22T14:41:01.301
60�˥�666
[104, 74, 0, 74, 0, 104, 201, 1, 128, 226, 15, 0, 2, 116, 0, 0, 4, 0, 0, 64, 20, 34, 6, 37, 86, 22]

2025-06-22T14:41:01.301
��8

2025-06-22T14:41:01.301
Could not initialize class com.xjy.test.mybatis.util.MyBatisUtil

2025-06-22T14:41:37.848
�ޥ��ChannelHandlerContext(XtProtocolDecoder#0, [id: 0x867b9c38, /192.168.1.200:4110 => /192.168.1.98:2090])

2025-06-22T14:41:37.848
����-h800104066
�e�ctxChannelHandlerContext(XtMessageHandler#0, [id: 0x9461720d, /192.168.1.200:4108 :> /192.168.1.98:2090])  false
��ctx:ChannelHandlerContext(XtProtocolDecoder#0, [id: 0x867b9c38, /192.168.1.200:4110 => /192.168.1.98:2090])    true

2025-06-22T14:41:40.939
�-h800104066
ctxChannelHandlerContext(XtMessageHandler#0, [id: 0x867b9c38, /192.168.1.200:4110 => /192.168.1.98:2090])  true

2025-06-22T14:41:40.949
����222
68 32 00 32 00 68 0b 01 ffffff80 ffffffe2 0f fffffff2 00 65 00 00 01 00 ffffffd5 16 

2025-06-22T14:46:01.027
60�˥�666
[104, 74, 0, 74, 0, 104, 201, 1, 128, 226, 15, 0, 2, 118, 0, 0, 4, 0, 0, 69, 20, 34, 6, 37, 93, 22]

2025-06-22T14:46:01.027
��8

2025-06-22T14:46:01.032
Could not initialize class com.xjy.test.mybatis.util.MyBatisUtil

2025-06-22T14:46:52.845
�ޥ��ChannelHandlerContext(XtProtocolDecoder#0, [id: 0x0d47011e, /192.168.1.200:4112 => /192.168.1.98:2090])

2025-06-22T14:46:52.845
����-h800104066
�e�ctxChannelHandlerContext(XtMessageHandler#0, [id: 0x867b9c38, /192.168.1.200:4110 :> /192.168.1.98:2090])  false
��ctx:ChannelHandlerContext(XtProtocolDecoder#0, [id: 0x0d47011e, /192.168.1.200:4112 => /192.168.1.98:2090])    true

2025-06-22T14:46:55.860
�-h800104066
ctxChannelHandlerContext(XtMessageHandler#0, [id: 0x0d47011e, /192.168.1.200:4112 => /192.168.1.98:2090])  true

2025-06-22T14:46:55.861
����222
68 32 00 32 00 68 0b 01 ffffff80 ffffffe2 0f fffffff2 00 67 00 00 01 00 ffffffd7 16 

2025-06-22T14:51:00.698
60�˥�666
[104, 74, 0, 74, 0, 104, 201, 1, 128, 226, 15, 0, 2, 120, 0, 0, 4, 0, 0, 80, 20, 34, 6, 37, 106, 22]

2025-06-22T14:51:00.700
��8

2025-06-22T14:51:00.701
Could not initialize class com.xjy.test.mybatis.util.MyBatisUtil

2025-06-22T14:51:36.379
�ޥ��ChannelHandlerContext(XtProtocolDecoder#0, [id: 0xbf7495bf, /192.168.1.200:4114 => /192.168.1.98:2090])

2025-06-22T14:51:36.379
����-h800104066
�e�ctxChannelHandlerContext(XtMessageHandler#0, [id: 0x0d47011e, /192.168.1.200:4112 :> /192.168.1.98:2090])  false
��ctx:ChannelHandlerContext(XtProtocolDecoder#0, [id: 0xbf7495bf, /192.168.1.200:4114 => /192.168.1.98:2090])    true

2025-06-22T14:51:39.445
�-h800104066
ctxChannelHandlerContext(XtMessageHandler#0, [id: 0xbf7495bf, /192.168.1.200:4114 => /192.168.1.98:2090])  true

2025-06-22T14:51:39.455
����222
68 32 00 32 00 68 0b 01 ffffff80 ffffffe2 0f fffffff2 00 69 00 00 01 00 ffffffd9 16 

2025-06-22T14:56:01.469
60�˥�666
[104, 74, 0, 74, 0, 104, 201, 1, 128, 226, 15, 0, 2, 122, 0, 0, 4, 0, 0, 85, 20, 34, 6, 37, 113, 22]

2025-06-22T14:56:01.477
��8

2025-06-22T14:56:01.477
Could not initialize class com.xjy.test.mybatis.util.MyBatisUtil

2025-06-22T14:56:51.317
�ޥ��ChannelHandlerContext(XtProtocolDecoder#0, [id: 0x281c51dc, /192.168.1.200:4116 => /192.168.1.98:2090])

2025-06-22T14:56:51.318
����-h800104066
�e�ctxChannelHandlerContext(XtMessageHandler#0, [id: 0xbf7495bf, /192.168.1.200:4114 :> /192.168.1.98:2090])  false
��ctx:ChannelHandlerContext(XtProtocolDecoder#0, [id: 0x281c51dc, /192.168.1.200:4116 => /192.168.1.98:2090])    true

2025-06-22T14:56:54.351
�-h800104066
ctxChannelHandlerContext(XtMessageHandler#0, [id: 0x281c51dc, /192.168.1.200:4116 => /192.168.1.98:2090])  true

2025-06-22T14:56:54.353
����222
68 32 00 32 00 68 0b 01 ffffff80 ffffffe2 0f fffffff2 00 6b 00 00 01 00 ffffffdb 16 

2025-06-22T15:01:01.406
60�˥�666
[104, 74, 0, 74, 0, 104, 201, 1, 128, 226, 15, 0, 2, 124, 0, 0, 4, 0, 0, 0, 21, 34, 6, 37, 31, 22]

2025-06-22T15:01:01.409
��8

2025-06-22T15:01:01.409
Could not initialize class com.xjy.test.mybatis.util.MyBatisUtil

2025-06-22T15:01:34.898
�ޥ��ChannelHandlerContext(XtProtocolDecoder#0, [id: 0x1bdfa181, /192.168.1.200:4118 => /192.168.1.98:2090])

2025-06-22T15:01:34.899
����-h800104066
�e�ctxChannelHandlerContext(XtMessageHandler#0, [id: 0x281c51dc, /192.168.1.200:4116 :> /192.168.1.98:2090])  false
��ctx:ChannelHandlerContext(XtProtocolDecoder#0, [id: 0x1bdfa181, /192.168.1.200:4118 => /192.168.1.98:2090])    true

2025-06-22T15:01:37.855
�-h800104066
ctxChannelHandlerContext(XtMessageHandler#0, [id: 0x1bdfa181, /192.168.1.200:4118 => /192.168.1.98:2090])  true

2025-06-22T15:01:37.856
����222
68 32 00 32 00 68 0b 01 ffffff80 ffffffe2 0f fffffff2 00 6d 00 00 01 00 ffffffdd 16 

2025-06-22T15:06:01.018
60�˥�666
[104, 74, 0, 74, 0, 104, 201, 1, 128, 226, 15, 0, 2, 126, 0, 0, 4, 0, 0, 5, 21, 34, 6, 37, 38, 22]

2025-06-22T15:06:01.018
��8

2025-06-22T15:06:01.022
Could not initialize class com.xjy.test.mybatis.util.MyBatisUtil

2025-06-22T15:06:49.823
�ޥ��ChannelHandlerContext(XtProtocolDecoder#0, [id: 0xadac0da7, /192.168.1.200:4120 => /192.168.1.98:2090])

2025-06-22T15:06:49.825
����-h800104066
�e�ctxChannelHandlerContext(XtMessageHandler#0, [id: 0x1bdfa181, /192.168.1.200:4118 :> /192.168.1.98:2090])  false
��ctx:ChannelHandlerContext(XtProtocolDecoder#0, [id: 0xadac0da7, /192.168.1.200:4120 => /192.168.1.98:2090])    true

2025-06-22T15:06:52.835
�-h800104066
ctxChannelHandlerContext(XtMessageHandler#0, [id: 0xadac0da7, /192.168.1.200:4120 => /192.168.1.98:2090])  true

2025-06-22T15:06:52.837
����222
68 32 00 32 00 68 0b 01 ffffff80 ffffffe2 0f fffffff2 00 6f 00 00 01 00 ffffffdf 16 

